version: '3.8'

services:
  fuelcost-api:
    build:
      context: ../..
      dockerfile: Microservices/FuelCostService/Dockerfile
    ports:
      - "8080:8080"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:8080
      - ConnectionStrings__DefaultConnection=Server=fuelcost-db;Database=fuelcost;User Id=sa;Password=YourStrong@Passw0rd;TrustServerCertificate=true;
    depends_on:
      - fuelcost-db
    networks:
      - fuelcost-network

  fuelcost-db:
    image: mcr.microsoft.com/mssql/server:2022-latest
    environment:
      - ACCEPT_EULA=Y
      - SA_PASSWORD=YourStrong@Passw0rd
      - MSSQL_PID=Express
    ports:
      - "1433:1433"
    volumes:
      - fuelcost-db-data:/var/opt/mssql
    networks:
      - fuelcost-network

volumes:
  fuelcost-db-data:

networks:
  fuelcost-network:
    driver: bridge