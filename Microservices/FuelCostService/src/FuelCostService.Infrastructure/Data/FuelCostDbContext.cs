using Microsoft.EntityFrameworkCore;
using FuelCostService.Core.Models;

namespace FuelCostService.Infrastructure.Data
{
    /// <summary>
    /// Entity Framework DbContext for fuel cost data
    /// </summary>
    public class FuelCostDbContext : DbContext
    {
        public FuelCostDbContext(DbContextOptions<FuelCostDbContext> options) : base(options)
        {
        }

        public DbSet<FuelCosts> FuelCosts { get; set; } = null!;
        public DbSet<FuelCostByType> FuelCostByType { get; set; } = null!;
        public DbSet<FuelCostRateBlocks> FuelCostRateBlocks { get; set; } = null!;
        public DbSet<FuelCostRateBlock> FuelCostRateBlock { get; set; } = null!;
        public DbSet<FuelCostMinimum> FuelCostMinimum { get; set; } = null!;
        public DbSet<FuelCostsMonthly> FuelCostsMonthly { get; set; } = null!;
        public DbSet<FuelCostMonthlyData> FuelCostMonthlyData { get; set; } = null!;
        public DbSet<CodeAndText> CodeAndText { get; set; } = null!;

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Set default schema
            modelBuilder.HasDefaultSchema("fuelcost");

            // Configure FuelCosts entity (main aggregate root)
            modelBuilder.Entity<FuelCosts>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.HouseId).IsRequired();
                entity.Property(e => e.EnergyUpgradeId).IsRequired(false);
                entity.Property(e => e.IncludeCostCalculations).IsRequired();
                entity.Property(e => e.LibraryFile).HasMaxLength(500);

                // Ignore navigation properties - we'll handle FuelCostByType separately
                entity.Ignore(e => e.Electricity);
                entity.Ignore(e => e.NaturalGas);
                entity.Ignore(e => e.Oil);
                entity.Ignore(e => e.Propane);
                entity.Ignore(e => e.Wood);

                // Configure one-to-one relationship with Monthly
                entity.HasOne(e => e.Monthly)
                      .WithOne(m => m.FuelCosts)
                      .HasForeignKey<FuelCostsMonthly>(m => m.FuelCostsId)
                      .OnDelete(DeleteBehavior.Cascade);

                // Indexes for performance
                entity.HasIndex(e => e.HouseId);
                entity.HasIndex(e => e.EnergyUpgradeId);
                entity.HasIndex(e => new { e.HouseId, e.EnergyUpgradeId }).IsUnique();
            });

            // Configure FuelCostByType entity
            modelBuilder.Entity<FuelCostByType>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.FuelCostsId).IsRequired();
                entity.Property(e => e.InternalId).IsRequired();
                entity.Property(e => e.Label).IsRequired().HasMaxLength(255);
                entity.Property(e => e.Comment).HasMaxLength(1000);

                // Configure one-to-one relationships
                entity.HasOne(e => e.Minimum)
                      .WithOne(m => m.FuelCostByType)
                      .HasForeignKey<FuelCostMinimum>(m => m.FuelCostByTypeId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.RateBlocks)
                      .WithOne(r => r.FuelCostByType)
                      .HasForeignKey<FuelCostRateBlocks>(r => r.FuelCostByTypeId)
                      .OnDelete(DeleteBehavior.Cascade);

                // Ignore Units navigation property for now - CodeAndText needs special handling
                entity.Ignore(e => e.Units);

                // Indexes
                entity.HasIndex(e => e.FuelCostsId);
                entity.HasIndex(e => new { e.FuelCostsId, e.InternalId });
            });

            // Configure FuelCostRateBlocks entity
            modelBuilder.Entity<FuelCostRateBlocks>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.FuelCostByTypeId).IsRequired();

                // Configure relationships with rate blocks
                entity.HasOne(e => e.Block1)
                      .WithOne()
                      .HasForeignKey<FuelCostRateBlock>("Block1Id")
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Block2)
                      .WithOne()
                      .HasForeignKey<FuelCostRateBlock>("Block2Id")
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Block3)
                      .WithOne()
                      .HasForeignKey<FuelCostRateBlock>("Block3Id")
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Block4)
                      .WithOne()
                      .HasForeignKey<FuelCostRateBlock>("Block4Id")
                      .OnDelete(DeleteBehavior.Cascade);
            });

            // Configure FuelCostRateBlock entity
            modelBuilder.Entity<FuelCostRateBlock>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.FuelCostByTypeId).IsRequired();
                entity.Property(e => e.Units).HasColumnType("decimal(18,6)");
                entity.Property(e => e.CostPerUnit).HasColumnType("decimal(18,6)");

                // Ignore the navigation property to avoid circular reference
                entity.Ignore(e => e.FuelCostByType);
            });

            // Configure FuelCostMinimum entity
            modelBuilder.Entity<FuelCostMinimum>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.FuelCostByTypeId).IsRequired();
                entity.Property(e => e.Units).HasColumnType("decimal(18,6)");
                entity.Property(e => e.Charge).HasColumnType("decimal(18,6)");
            });

            // Configure FuelCostsMonthly entity
            modelBuilder.Entity<FuelCostsMonthly>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.FuelCostsId).IsRequired();

                // Configure relationships with monthly data
                entity.HasOne(e => e.Electricity)
                      .WithOne()
                      .HasForeignKey<FuelCostMonthlyData>("ElectricityId")
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.NaturalGas)
                      .WithOne()
                      .HasForeignKey<FuelCostMonthlyData>("NaturalGasId")
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Oil)
                      .WithOne()
                      .HasForeignKey<FuelCostMonthlyData>("OilId")
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Propane)
                      .WithOne()
                      .HasForeignKey<FuelCostMonthlyData>("PropaneId")
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Wood)
                      .WithOne()
                      .HasForeignKey<FuelCostMonthlyData>("WoodId")
                      .OnDelete(DeleteBehavior.Cascade);
            });

            // Configure FuelCostMonthlyData entity
            modelBuilder.Entity<FuelCostMonthlyData>(entity =>
            {
                entity.HasKey(e => e.Id);
                // Monthly values are stored as strings like in the original
                entity.Property(e => e.January).HasMaxLength(50);
                entity.Property(e => e.February).HasMaxLength(50);
                entity.Property(e => e.March).HasMaxLength(50);
                entity.Property(e => e.April).HasMaxLength(50);
                entity.Property(e => e.May).HasMaxLength(50);
                entity.Property(e => e.June).HasMaxLength(50);
                entity.Property(e => e.July).HasMaxLength(50);
                entity.Property(e => e.August).HasMaxLength(50);
                entity.Property(e => e.September).HasMaxLength(50);
                entity.Property(e => e.October).HasMaxLength(50);
                entity.Property(e => e.November).HasMaxLength(50);
                entity.Property(e => e.December).HasMaxLength(50);
            });

            // Configure CodeAndText entity
            modelBuilder.Entity<CodeAndText>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Code).IsRequired().HasMaxLength(50);
                entity.Property(e => e.Text).HasMaxLength(255);
            });
        }
    }
}